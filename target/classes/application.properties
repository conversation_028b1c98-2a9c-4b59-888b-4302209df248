# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Database Configuration - Using H2 in-memory database
spring.datasource.url=jdbc:h2:mem:photo_app;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console Configuration
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA / Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true

# File Storage Configuration
app.file.storage.location=uploads
app.file.storage.type=local
# app.file.storage.type=s3 # Uncomment to use S3 instead of local storage

# S3 Configuration (if using S3 storage)
# app.s3.access-key=your-access-key
# app.s3.secret-key=your-secret-key
# app.s3.bucket-name=your-bucket-name
# app.s3.region=your-region

# Aliyun OSS Configuration (if using Aliyun OSS)
# app.aliyun.oss.endpoint=your-endpoint
# app.aliyun.oss.access-key-id=your-access-key-id
# app.aliyun.oss.access-key-secret=your-access-key-secret
# app.aliyun.oss.bucket-name=your-bucket-name

# Thumbnail Configuration
app.thumbnail.sizes=small:200x200,medium:400x400,large:800x800

# JWT Configuration
app.jwt.secret=your-jwt-secret-key-should-be-very-long-and-secure
app.jwt.expiration=86400000
app.jwt.refresh-expiration=604800000

# Redis Configuration (for caching)
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.timeout=60000

# Logging Configuration
logging.level.root=INFO
logging.level.com.photoapp=DEBUG
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR

# API Rate Limiting
app.rate-limit.enabled=true
app.rate-limit.limit=100
app.rate-limit.refresh-period=60
