package com.photoapp.repository;

import com.photoapp.model.Photo;
import com.photoapp.model.Thumbnail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ThumbnailRepository extends JpaRepository<Thumbnail, Long> {
    List<Thumbnail> findByPhotoId(Long photoId);
    
    List<Thumbnail> findByPhoto(Photo photo);
    
    Optional<Thumbnail> findByPhotoIdAndSizeType(Long photoId, String sizeType);
    
    Optional<Thumbnail> findByPhotoAndSizeType(Photo photo, String sizeType);
    
    void deleteByPhotoId(Long photoId);
}
