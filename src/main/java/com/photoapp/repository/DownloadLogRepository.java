package com.photoapp.repository;

import com.photoapp.model.DownloadLog;
import com.photoapp.model.Photo;
import com.photoapp.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface DownloadLogRepository extends JpaRepository<DownloadLog, Long> {
    List<DownloadLog> findByPhotoId(Long photoId);
    
    List<DownloadLog> findByPhoto(Photo photo);
    
    List<DownloadLog> findByUserId(Long userId);
    
    List<DownloadLog> findByUser(User user);
    
    Page<DownloadLog> findByPhotoId(Long photoId, Pageable pageable);
    
    Page<DownloadLog> findByUserId(Long userId, Pageable pageable);
    
    List<DownloadLog> findByDownloadedAtBetween(LocalDateTime start, LocalDateTime end);
    
    long countByPhotoId(Long photoId);
    
    long countByUserId(Long userId);
}
