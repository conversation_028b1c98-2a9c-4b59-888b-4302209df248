# Photo Upload/Download System

A Spring Boot-based web application for managing photo uploads, downloads, and metadata.

## Features

- User authentication with JWT
- Photo upload with validation
- Photo download and streaming
- Automatic thumbnail generation
- Photo metadata extraction
- RESTful APIs
- Swagger documentation

## Quick Start

1. **Build and run:**
   ```bash
   mvn clean install
   mvn spring-boot:run
   ```

2. **Access the application:**
   - API Base URL: `http://localhost:8080/api`
   - Swagger UI: `http://localhost:8080/api/swagger-ui.html`
   - H2 Console: `http://localhost:8080/api/h2-console`

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh JWT token

### Photos
- `GET /photos` - List user photos
- `POST /photos/upload` - Upload photo
- `GET /photos/{id}` - Get photo metadata
- `GET /photos/{id}/download` - Download photo
- `GET /photos/{id}/thumbnail` - Get thumbnail
- `DELETE /photos/{id}` - Delete photo

### Users
- `GET /users/profile` - Get user profile

## Technology Stack

- Spring Boot 3.x
- Spring Security with JWT
- Spring Data JPA
- H2 Database (development)
- Thumbnailator (image processing)
- OpenAPI/Swagger
- Maven

## Configuration

Key configuration properties in `application.yml`:

```yaml
app:
  upload:
    path: ./uploads
    max-file-size: 52428800
  thumbnail:
    path: ./thumbnails
    sizes:
      small: 150
      medium: 300
      large: 600
```

## Usage Example

1. **Register:**
   ```bash
   curl -X POST http://localhost:8080/api/auth/register \
     -H "Content-Type: application/json" \
     -d '{"username":"user","email":"<EMAIL>","password":"password123"}'
   ```

2. **Upload photo:**
   ```bash
   curl -X POST http://localhost:8080/api/photos/upload \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -F "file=@photo.jpg"
   ```

3. **Download photo:**
   ```bash
   curl -X GET http://localhost:8080/api/photos/1/download \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     --output photo.jpg
   ``` 