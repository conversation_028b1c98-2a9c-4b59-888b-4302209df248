package com.photoapp.repository;

import com.photoapp.model.Photo;
import com.photoapp.model.PhotoMetadata;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PhotoMetadataRepository extends JpaRepository<PhotoMetadata, Long> {
    Optional<PhotoMetadata> findByPhotoId(Long photoId);
    
    Optional<PhotoMetadata> findByPhoto(Photo photo);
    
    void deleteByPhotoId(Long photoId);
}
