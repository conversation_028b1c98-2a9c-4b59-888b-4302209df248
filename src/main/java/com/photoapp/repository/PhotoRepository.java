package com.photoapp.repository;

import com.photoapp.model.Photo;
import com.photoapp.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PhotoRepository extends JpaRepository<Photo, Long> {
    Page<Photo> findByUserAndIsDeletedFalse(User user, Pageable pageable);
    
    List<Photo> findByUserAndIsDeletedFalse(User user);
    
    Optional<Photo> findByIdAndUserAndIsDeletedFalse(Long id, User user);
    
    Optional<Photo> findByIdAndIsDeletedFalse(Long id);
    
    boolean existsByIdAndUserAndIsDeletedFalse(Long id, User user);
}
