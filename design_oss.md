# OSS Storage Integration Design Document

## 1. Overview

This document outlines the design for integrating Object Storage Service (OSS) into the existing photo upload/download application. The integration will replace the current local file storage system with a cloud-based OSS solution, providing better scalability, reliability, and performance.

## 2. Current Architecture Analysis

### 2.1 Current File Storage Architecture

The existing system uses local file storage with the following components:
- `FileStorageService`: Handles file operations on local filesystem
- `PhotoService`: Manages photo business logic
- Local directories for uploads and thumbnails

### 2.2 Limitations of Current System

1. **Scalability**: Limited by local disk space
2. **Reliability**: Single point of failure
3. **Performance**: No CDN support
4. **Backup**: Manual backup processes required
5. **Multi-instance**: Difficult to scale horizontally

## 3. OSS Integration Design

### 3.1 Design Goals

1. **Seamless Integration**: Minimal changes to existing business logic
2. **Backward Compatibility**: Support for existing local files during migration
3. **Performance**: Improved upload/download speeds
4. **Scalability**: Unlimited storage capacity
5. **Security**: Secure access controls and encryption
6. **Cost Optimization**: Intelligent storage class management

### 3.2 Architecture Overview

```mermaid
graph TB
    subgraph "Client Layer"
        A[Web Client]
        B[Mobile App]
    end
    
    subgraph "API Layer"
        C[Photo Controller]
        D[Auth Controller]
    end
    
    subgraph "Service Layer"
        E[Photo Service]
        F[OSS Storage Service]
        G[Image Processing Service]
        H[Cache Service]
    end
    
    subgraph "Storage Layer"
        I[Database]
        J[Redis Cache]
        K[OSS Bucket]
        L[CDN]
    end
    
    A --> C
    B --> C
    C --> E
    E --> F
    E --> G
    F --> K
    K --> L
    E --> I
    E --> H
    H --> J
    
    style K fill:#e1f5fe
    style L fill:#e8f5e8
    style F fill:#fff3e0
```

### 3.3 Component Design

#### 3.3.1 OSS Storage Service Interface

```mermaid
classDiagram
    class StorageService {
        <<interface>>
        +storeFile(file: MultipartFile, path: String) String
        +storeBytes(data: byte[], path: String, contentType: String) String
        +loadFile(path: String) byte[]
        +deleteFile(path: String) boolean
        +generatePresignedUrl(path: String, expiration: Duration) String
        +copyFile(sourcePath: String, targetPath: String) boolean
        +getFileMetadata(path: String) FileMetadata
    }
    
    class LocalFileStorageService {
        -uploadPath: String
        -thumbnailPath: String
        +storeFile(file: MultipartFile, path: String) String
        +loadFile(path: String) byte[]
        +deleteFile(path: String) boolean
    }
    
    class OSSStorageService {
        -ossClient: OSSClient
        -bucketName: String
        -cdnDomain: String
        +storeFile(file: MultipartFile, path: String) String
        +generatePresignedUrl(path: String, expiration: Duration) String
        +setStorageClass(path: String, storageClass: StorageClass) boolean
    }
    
    class HybridStorageService {
        -primaryStorage: StorageService
        -fallbackStorage: StorageService
        +storeFile(file: MultipartFile, path: String) String
        +loadFile(path: String) byte[]
    }
    
    class FileMetadata {
        +size: Long
        +contentType: String
        +lastModified: LocalDateTime
        +etag: String
        +storageClass: String
    }
    
    StorageService <|.. LocalFileStorageService
    StorageService <|.. OSSStorageService
    StorageService <|.. HybridStorageService
    OSSStorageService --> FileMetadata
    HybridStorageService --> StorageService
```

#### 3.3.2 Enhanced Photo Model

```mermaid
classDiagram
    class Photo {
        +id: Long
        +user: User
        +originalFilename: String
        +storedFilename: String
        +filePath: String
        +contentType: String
        +fileSize: Long
        +checksum: String
        +storageType: StorageType
        +ossPath: String
        +cdnUrl: String
        +storageClass: String
        +createdAt: LocalDateTime
        +updatedAt: LocalDateTime
        +isDeleted: Boolean
    }
    
    class StorageType {
        <<enumeration>>
        LOCAL
        OSS
        HYBRID
    }
    
    class StorageClass {
        <<enumeration>>
        STANDARD
        INFREQUENT_ACCESS
        ARCHIVE
        COLD_ARCHIVE
    }
    
    Photo --> StorageType
    Photo --> StorageClass
```

### 3.4 Configuration Design

```mermaid
classDiagram
    class OSSConfig {
        +endpoint: String
        +accessKeyId: String
        +accessKeySecret: String
        +bucketName: String
        +cdnDomain: String
        +region: String
        +maxConnections: Integer
        +connectionTimeout: Integer
        +socketTimeout: Integer
    }
    
    class StorageConfig {
        +storageType: StorageType
        +migrationEnabled: Boolean
        +hybridMode: Boolean
        +defaultStorageClass: String
        +autoTiering: Boolean
        +presignedUrlExpiration: Duration
    }
    
    class ThumbnailConfig {
        +sizes: Map<String, Integer>
        +quality: Float
        +format: String
        +storageClass: String
    }
    
    OSSConfig --> StorageConfig
    StorageConfig --> ThumbnailConfig
```

## 4. Sequence Diagrams

### 4.1 Photo Upload Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant PC as PhotoController
    participant PS as PhotoService
    participant OSS as OSSStorageService
    participant IPS as ImageProcessingService
    participant DB as Database
    participant Cache as Redis
    
    C->>PC: POST /photos (multipart file)
    PC->>PS: uploadPhoto(file, user)
    PS->>PS: validateFile(file)
    PS->>OSS: storeFile(file, path)
    OSS->>OSS: generateUniqueKey()
    OSS->>OSS: uploadToOSS(file, key)
    OSS-->>PS: return ossPath
    PS->>IPS: extractMetadata(file)
    IPS-->>PS: return metadata
    PS->>DB: save(photo)
    PS->>IPS: generateThumbnails(file)
    IPS->>OSS: storeThumbnails(thumbnails)
    OSS-->>IPS: return thumbnailPaths
    PS->>DB: updateThumbnails(photo)
    PS->>Cache: cachePhotoInfo(photoId, info)
    PS-->>PC: return PhotoResponse
    PC-->>C: return 201 Created
```

### 4.2 Photo Download Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant PC as PhotoController
    participant PS as PhotoService
    participant OSS as OSSStorageService
    participant Cache as Redis
    participant DB as Database
    
    C->>PC: GET /photos/{id}/download
    PC->>PS: downloadPhoto(photoId, user)
    PS->>Cache: getPhotoInfo(photoId)
    alt Cache Hit
        Cache-->>PS: return photoInfo
    else Cache Miss
        PS->>DB: findPhoto(photoId, user)
        DB-->>PS: return photo
        PS->>Cache: cachePhotoInfo(photoId, photo)
    end
    
    alt Direct Download (Small Files)
        PS->>OSS: loadFile(ossPath)
        OSS-->>PS: return fileBytes
        PS-->>PC: return fileBytes
    else Presigned URL (Large Files)
        PS->>OSS: generatePresignedUrl(ossPath)
        OSS-->>PS: return presignedUrl
        PS-->>PC: return redirect(presignedUrl)
    end
    
    PC-->>C: return file/redirect
```

### 4.3 Migration Flow

```mermaid
sequenceDiagram
    participant MS as MigrationService
    participant DB as Database
    participant LFS as LocalFileStorage
    participant OSS as OSSStorageService
    participant Cache as Redis
    
    MS->>DB: findPhotosToMigrate(batchSize)
    DB-->>MS: return photoList
    
    loop For each photo
        MS->>LFS: loadFile(localPath)
        LFS-->>MS: return fileBytes
        MS->>OSS: storeFile(fileBytes, ossPath)
        OSS-->>MS: return success
        MS->>DB: updatePhoto(storageType=OSS, ossPath)
        MS->>Cache: invalidateCache(photoId)
        
        alt Cleanup enabled
            MS->>LFS: deleteFile(localPath)
        end
    end
    
    MS->>MS: logMigrationProgress()
```

## 5. Flow Charts

### 5.1 Storage Strategy Decision Flow

```mermaid
flowchart TD
    A[File Upload Request] --> B{File Size Check}
    B -->|< 10MB| C[Direct OSS Upload]
    B -->|>= 10MB| D[Multipart Upload]
    
    C --> E{Storage Class Decision}
    D --> E
    
    E -->|Frequent Access Expected| F[Standard Storage]
    E -->|Infrequent Access| G[IA Storage]
    E -->|Archive| H[Archive Storage]
    
    F --> I[Generate Thumbnails]
    G --> I
    H --> I
    
    I --> J[Store Metadata in DB]
    J --> K[Cache Photo Info]
    K --> L[Return Success Response]
```

### 5.2 Auto-Tiering Flow

```mermaid
flowchart TD
    A[Scheduled Job Runs] --> B[Query Photos by Age/Access Pattern]
    B --> C{Photo Age > 30 days?}
    C -->|No| D[Keep in Standard]
    C -->|Yes| E{Access Count < Threshold?}
    E -->|No| D
    E -->|Yes| F{Age > 90 days?}
    F -->|No| G[Move to IA Storage]
    F -->|Yes| H{Age > 365 days?}
    H -->|No| G
    H -->|Yes| I[Move to Archive]
    
    G --> J[Update Storage Class in DB]
    I --> J
    J --> K[Log Tiering Action]
    K --> L[Update Cost Metrics]
```

### 5.3 Error Handling Flow

```mermaid
flowchart TD
    A[OSS Operation] --> B{Operation Success?}
    B -->|Yes| C[Return Success]
    B -->|No| D{Retry Attempt < Max?}
    D -->|Yes| E[Exponential Backoff]
    E --> F[Retry Operation]
    F --> B
    D -->|No| G{Fallback Available?}
    G -->|Yes| H[Use Fallback Storage]
    G -->|No| I[Log Error & Notify]
    H --> J{Fallback Success?}
    J -->|Yes| K[Mark for Later Sync]
    J -->|No| I
    I --> L[Return Error Response]
```

## 6. Implementation Plan

### 6.1 Phase 1: Core OSS Integration

#### 6.1.1 Dependencies
Add OSS SDK dependency to `pom.xml`:

```xml
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>3.17.4</version>
</dependency>
```

#### 6.1.2 Configuration
Update `application.yml`:

```yaml
app:
  storage:
    type: OSS # LOCAL, OSS, HYBRID
    migration-enabled: true
    hybrid-mode: false
    default-storage-class: STANDARD
    auto-tiering: true
    presigned-url-expiration: PT1H
  
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    bucket-name: photo-app-bucket
    cdn-domain: https://cdn.example.com
    region: cn-hangzhou
    max-connections: 100
    connection-timeout: 5000
    socket-timeout: 10000
    access-key-id: ${OSS_ACCESS_KEY_ID}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET}
```

#### 6.1.3 Service Implementation

```java
@Service
public class OSSStorageService implements StorageService {
    private final OSSClient ossClient;
    private final String bucketName;
    private final String cdnDomain;
    
    // Implementation details...
}
```

### 6.2 Phase 2: Migration Strategy

#### 6.2.1 Database Schema Updates

```sql
ALTER TABLE photos 
ADD COLUMN storage_type VARCHAR(20) DEFAULT 'LOCAL',
ADD COLUMN oss_path VARCHAR(500),
ADD COLUMN cdn_url VARCHAR(500),
ADD COLUMN storage_class VARCHAR(50) DEFAULT 'STANDARD';

CREATE INDEX idx_photos_storage_type ON photos(storage_type);
CREATE INDEX idx_photos_oss_path ON photos(oss_path);
```

#### 6.2.2 Migration Service

```java
@Service
public class PhotoMigrationService {
    @Scheduled(fixedDelay = 60000) // Run every minute
    public void migratePhotos() {
        // Batch migration logic
    }
}
```

### 6.3 Phase 3: Advanced Features

#### 6.3.1 Auto-Tiering Service

```java
@Service
public class StorageTieringService {
    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    public void performAutoTiering() {
        // Auto-tiering logic based on access patterns
    }
}
```

#### 6.3.2 CDN Integration

```java
@Service
public class CDNService {
    public String generateCDNUrl(String ossPath) {
        return cdnDomain + "/" + ossPath;
    }
    
    public void preloadToCache(List<String> paths) {
        // CDN cache preloading
    }
}
```

## 7. Security Considerations

### 7.1 Access Control

```mermaid
graph TD
    A[Client Request] --> B[JWT Validation]
    B --> C[User Authorization]
    C --> D[Resource Ownership Check]
    D --> E[Generate Presigned URL]
    E --> F[Set Expiration Time]
    F --> G[Return Secure URL]
    
    style E fill:#ffebee
    style F fill:#ffebee
```

### 7.2 Data Encryption

1. **In Transit**: HTTPS/TLS for all API calls
2. **At Rest**: OSS server-side encryption (SSE-OSS)
3. **Client-side**: Optional client-side encryption for sensitive data

### 7.3 Access Patterns

```yaml
OSS Bucket Policy:
  - Effect: Allow
    Principal: PhotoApp
    Action: 
      - oss:PutObject
      - oss:GetObject
      - oss:DeleteObject
    Resource: photo-app-bucket/photos/*
    Condition:
      StringEquals:
        oss:x-oss-server-side-encryption: AES256
```

## 8. Performance Optimization

### 8.1 Caching Strategy

```mermaid
graph LR
    A[Client Request] --> B[Redis Cache]
    B -->|Cache Hit| C[Return Cached Data]
    B -->|Cache Miss| D[OSS Storage]
    D --> E[Cache Result]
    E --> F[Return Data]
    
    style B fill:#e3f2fd
    style E fill:#e3f2fd
```

### 8.2 CDN Configuration

1. **Cache Rules**: 
   - Images: 30 days
   - Thumbnails: 7 days
   - Metadata: 1 hour

2. **Compression**: Enable Gzip for metadata responses

3. **Geographic Distribution**: Multi-region CDN deployment

## 9. Monitoring and Metrics

### 9.1 Key Metrics

```mermaid
graph TD
    A[Monitoring Dashboard] --> B[Upload Success Rate]
    A --> C[Download Latency]
    A --> D[Storage Costs]
    A --> E[CDN Hit Rate]
    A --> F[Error Rates]
    A --> G[Migration Progress]
    
    style A fill:#e8f5e8
```

### 9.2 Alerting Rules

1. **Upload Failure Rate > 5%**: Critical Alert
2. **Download Latency > 2s**: Warning Alert
3. **Storage Cost Increase > 20%**: Budget Alert
4. **CDN Hit Rate < 80%**: Performance Alert

## 10. Cost Optimization

### 10.1 Storage Class Strategy

```mermaid
graph TD
    A[New Photo] --> B[Standard Storage]
    B --> C{30 Days Later}
    C --> D{Access Count < 10?}
    D -->|Yes| E[Move to IA]
    D -->|No| F[Keep Standard]
    E --> G{90 Days Later}
    G --> H{Access Count < 3?}
    H -->|Yes| I[Move to Archive]
    H -->|No| J[Keep IA]
    
    style E fill:#fff3e0
    style I fill:#fce4ec
```

### 10.2 Cost Monitoring

1. **Daily Cost Reports**: Automated cost breakdown
2. **Budget Alerts**: Threshold-based notifications
3. **Usage Analytics**: Storage and bandwidth trends
4. **Optimization Recommendations**: AI-driven suggestions

## 11. Testing Strategy

### 11.1 Unit Tests

```java
@Test
public void testOSSUpload() {
    // Mock OSS client
    // Test upload functionality
    // Verify success response
}

@Test
public void testPresignedURLGeneration() {
    // Test URL generation
    // Verify expiration time
    // Check URL format
}
```

### 11.2 Integration Tests

```java
@SpringBootTest
@TestPropertySource(properties = {
    "app.storage.type=OSS",
    "app.oss.bucket-name=test-bucket"
})
public class OSSIntegrationTest {
    // End-to-end OSS integration tests
}
```

### 11.3 Performance Tests

1. **Load Testing**: Concurrent upload/download scenarios
2. **Stress Testing**: High-volume operations
3. **Endurance Testing**: Long-running operations

## 12. Deployment Strategy

### 12.1 Blue-Green Deployment

```mermaid
graph LR
    A[Load Balancer] --> B[Blue Environment - Local Storage]
    A --> C[Green Environment - OSS Storage]
    
    style C fill:#e8f5e8
```

### 12.2 Feature Flags

```yaml
features:
  oss-storage:
    enabled: true
    rollout-percentage: 100
  hybrid-mode:
    enabled: false
    rollout-percentage: 0
```

## 13. Rollback Plan

### 13.1 Rollback Scenarios

1. **OSS Service Unavailable**: Automatic fallback to local storage
2. **Performance Degradation**: Feature flag to disable OSS
3. **Data Corruption**: Restore from backup

### 13.2 Rollback Procedure

```mermaid
flowchart TD
    A[Issue Detected] --> B[Assess Impact]
    B --> C{Critical Issue?}
    C -->|Yes| D[Immediate Rollback]
    C -->|No| E[Monitor & Fix]
    D --> F[Disable OSS Feature]
    F --> G[Switch to Local Storage]
    G --> H[Verify System Health]
    H --> I[Investigate Root Cause]
```

## 14. Conclusion

The OSS integration will significantly improve the scalability, reliability, and performance of the photo storage system. The phased implementation approach ensures minimal disruption to existing functionality while providing a clear migration path.

Key benefits:
- **Unlimited Scalability**: No storage capacity constraints
- **High Availability**: 99.9% uptime SLA
- **Global Performance**: CDN-accelerated downloads
- **Cost Efficiency**: Intelligent storage tiering
- **Security**: Enterprise-grade encryption and access controls

The hybrid approach during migration ensures zero downtime and provides fallback capabilities for enhanced reliability. 