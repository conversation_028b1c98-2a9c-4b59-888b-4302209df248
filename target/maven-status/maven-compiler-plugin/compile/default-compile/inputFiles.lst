/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/model/Thumbnail.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/dto/AuthRequest.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/PhotoAppApplication.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/repository/DownloadLogRepository.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/dto/RegisterRequest.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/service/AuthService.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/repository/UserRepository.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/dto/JwtResponse.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/repository/PhotoMetadataRepository.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/model/User.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/model/Photo.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/model/PhotoMetadata.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/model/DownloadLog.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/security/JwtTokenProvider.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/repository/ThumbnailRepository.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/security/SecurityConfig.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/security/JwtAuthenticationFilter.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/repository/PhotoRepository.java
/Users/<USER>/code/newprojectabc/src/main/java/com/photoapp/security/UserDetailsServiceImpl.java
