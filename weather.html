<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气预报</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #72b0d1, #f5f7fa);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        /* 容器的样式 */
        .container {
            max-width: 900px; /* 设置最大宽度 */
            margin: 0 auto; /* 水平居中 */
            background-color: rgba(255, 255, 255, 0.9); /* 背景颜色 */
            border-radius: 15px; /* 圆角 */
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* 阴影效果 */
            padding: 30px; /* 内边距 */
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        .weather-cards {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .weather-card {
            flex: 1;
            min-width: 250px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        
        .weather-card:hover {
            transform: translateY(-5px);
        }
        
        .day {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .date {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .weather-icon {
            font-size: 4rem;
            text-align: center;
            margin: 20px 0;
        }
        
        .temp {
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }
        
        .description {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        
        .details {
            border-top: 1px solid #eee;
            padding-top: 15px;
            margin-top: 15px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .detail-label {
            color: #7f8c8d;
        }
        
        .detail-value {
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .weather-cards {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- 容器开始 -->
    <div class="container">
        <h1>天气预报</h1>
        <!-- 天气卡片容器 -->
        <div class="weather-cards">
            <!-- 昨天的天气卡片 -->
            <div class="weather-card" id="yesterday">
                <div class="day">昨天</div>
                <div class="date" id="yesterday-date"></div>
                <div class="weather-icon">☀️</div>
                <div class="temp" id="yesterday-temp">24°C</div>
                <div class="description" id="yesterday-desc">晴朗</div>
                <div class="details">
                    <div class="detail-item">
                        <span class="detail-label">湿度:</span>
                        <span class="detail-value" id="yesterday-humidity">45%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">风速:</span>
                        <span class="detail-value" id="yesterday-wind">12 km/h</span>
                    </div>
                </div>
            </div>
            
            <!-- 今天的天气卡片 -->
            <div class="weather-card" id="today">
                <div class="day">今天</div>
                <div class="date" id="today-date"></div>
                <div class="weather-icon">⛅</div>
                <div class="temp" id="today-temp">26°C</div>
                <div class="description" id="today-desc">多云</div>
                <div class="details">
                    <div class="detail-item">
                        <span class="detail-label">湿度:</span>
                        <span class="detail-value" id="today-humidity">60%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">风速:</span>
                        <span class="detail-value" id="today-wind">8 km/h</span>
                    </div>
                </div>
            </div>
            
            <!-- 明天的天气卡片 -->
            <div class="weather-card" id="tomorrow">
                <div class="day">明天</div>
                <div class="date" id="tomorrow-date"></div>
                <div class="weather-icon">🌧️</div>
                <div class="temp" id="tomorrow-temp">22°C</div>
                <div class="description" id="tomorrow-desc">小雨</div>
                <div class="details">
                    <div class="detail-item">
                        <span class="detail-label">湿度:</span>
                        <span class="detail-value" id="tomorrow-humidity">85%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">风速:</span>
                        <span class="detail-value" id="tomorrow-wind">15 km/h</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设置日期
        const today = new Date(); // 获取当前日期
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1); // 获取昨天的日期
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1); // 获取明天的日期
    
        // 更新页面上的日期显示
        document.getElementById('yesterday-date').textContent = formatDate(yesterday);
        document.getElementById('today-date').textContent = formatDate(today);
        document.getElementById('tomorrow-date').textContent = formatDate(tomorrow);
    
        // 格式化日期的函数
        function formatDate(date) {
            const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
            return date.toLocaleDateString('zh-CN', options);
        }
    
        // 模拟天气数据
        const weatherData = {
            yesterday: {
                temp: '24°C',
                desc: '晴朗',
                humidity: '45%',
                wind: '12 km/h',
                icon: '☀️'
            },
            today: {
                temp: '26°C',
                desc: '多云',
                humidity: '60%',
                wind: '8 km/h',
                icon: '⛅'
            },
            tomorrow: {
                temp: '22°C',
                desc: '小雨',
                humidity: '85%',
                wind: '15 km/h',
                icon: '🌧️'
            }
        };
    
        // 更新天气数据的函数
        function updateWeather() {
            for (const day in weatherData) {
                const data = weatherData[day];
                document.getElementById(`${day}-temp`).textContent = data.temp;
                document.getElementById(`${day}-desc`).textContent = data.desc;
                document.getElementById(`${day}-humidity`).textContent = data.humidity;
                document.getElementById(`${day}-wind`).textContent = data.wind;
                document.querySelector(`#${day} .weather-icon`).textContent = data.icon;
            }
        }
    
        // 调用更新天气数据的函数
        updateWeather();
    
        // 这里可以添加从API获取真实天气数据的代码
        // 例如使用fetch请求天气API
    </script>
</body>
</html>